package kr.wayplus.qr_hallimpark.model.api;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * QR-문제 연결 생성 요청 모델
 * - API 서버에서 전송하는 연결 생성 요청 데이터
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QrQuizLink {

    private String qrCodeId;
    private String createId;
    private Long quizId;
    /**
     * 스토리형 콘텐츠 문제 노출 순서 (선택사항)
     */
    private Integer displayOrder;

    /**
     * QrQuizMapping 모델로 변환
     * @param createId 생성자 ID
     * @return QrQuizMapping
     */
    public QrQuizLink toQrQuizMapping() {
        return QrQuizLink.builder()
                .qrCodeId(this.qrCodeId)
                .quizId(this.quizId)
                .displayOrder(this.displayOrder)
                .createId(this.createId)
                .build();
    }
}
