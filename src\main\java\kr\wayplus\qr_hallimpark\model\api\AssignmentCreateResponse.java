package kr.wayplus.qr_hallimpark.model.api;

import kr.wayplus.qr_hallimpark.model.QrQuizMapping;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * QR-문제 연결 생성 응답 모델
 * - API 서버로 반환하는 연결 생성 결과 데이터
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssignmentCreateResponse {

    /**
     * 매핑 고유 ID
     */
    private Long mappingId;

    /**
     * QR 코드 고유 식별자
     */
    private String qrCodeId;

    /**
     * 문제 ID
     */
    private Long quizId;

    /**
     * 문제 제목
     */
    private String quizTitle;

    /**
     * 스토리형 콘텐츠 문제 노출 순서
     */
    private Integer displayOrder;

    /**
     * QrQuizMapping에서 AssignmentCreateResponse로 변환
     * @param mapping QrQuizMapping
     * @return AssignmentCreateResponse
     */
    public static AssignmentCreateResponse fromQrQuizMapping(QrQuizMapping mapping) {
        return AssignmentCreateResponse.builder()
                .mappingId(mapping.getMappingId())
                .qrCodeId(mapping.getQrCodeId())
                .quizId(mapping.getQuizId())
                .quizTitle(mapping.getQuizTitle())
                .displayOrder(mapping.getDisplayOrder())
                .build();
    }

}
